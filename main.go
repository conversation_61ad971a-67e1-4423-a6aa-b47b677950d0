package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	// Create manager with proper configuration
	manager := DefaultManager()

	// Set up error handler
	manager.SetErrorHandler(NewDefaultErrorHandler(manager.logger))

	// Set up router with basic handlers
	router := NewEventRouter()
	router.On("ping", handlePing)
	router.On("echo", handleEcho)
	manager.SetRouter(router)

	// HTTP handler for WebSocket upgrade
	http.HandleFunc("/ws", func(w http.ResponseWriter, r *http.Request) {
		handleWebSocket(manager, w, r)
	})

	// Start HTTP server
	server := &http.Server{
		Addr:         ":8080",
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	go func() {
		log.Println("WebSocket server starting on :8080")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()

	// Graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}

func handleWebSocket(manager *Manager, w http.ResponseWriter, r *http.Request) {
	conn, err := manager.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}

	client := manager.AddClient(conn, true)
	if client == nil {
		log.Printf("Failed to add client - server at capacity")
		conn.Close()
		return
	}

	log.Printf("Client connected: %s", client.Id)

	// Start client goroutines
	go manager.ReadPump(client)
	go manager.WritePump(client)
}

// Basic event handlers
func handlePing(c Ctx) error {
	response := NewMessage("pong", []byte(`{"message":"pong"}`))
	return c.WriteMessage(response)
}

func handleEcho(c Ctx) error {
	// Echo back the received data
	response := NewMessage("echo", c.GetMsg())
	return c.WriteMessage(response)
}
