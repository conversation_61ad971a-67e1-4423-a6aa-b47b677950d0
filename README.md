# WSWrapper - Simple WebSocket Manager

A secure, high-performance WebSocket manager package built with Go. Easy to integrate, highly customizable, and production-ready.

## ✅ Recent Improvements

### 🔴 Critical Fixes Applied
- **Fixed Error Handler Type Mismatch**: Proper ErrorHandler interface implementation
- **Fixed WritePump Message Writing**: Messages now actually get sent to WebSocket clients
- **Fixed Race Condition**: Safe channel closure using sync.Once
- **Fixed Client Lifecycle**: Proper goroutine management and cleanup
- **Removed Missing Methods**: Eliminated references to non-existent SubscribeTo/PublishTo methods

### 🟠 Architecture Improvements
- **Simplified Design**: Focused on core WebSocket functionality without pub/sub complexity
- **Better Error Handling**: Consistent error propagation and logging throughout
- **Improved CORS Security**: Removed wildcard origins, proper validation
- **Enhanced Client Management**: Better limits checking and resource cleanup
- **Thread-Safe Operations**: Proper mutex protection for all concurrent operations

### 🟡 Performance Optimizations
- **Eliminated Double JSON Processing**: Fixed inefficient marshal/unmarshal chains
- **Goroutine Leak Prevention**: Proper context cancellation and cleanup
- **Memory Leak Prevention**: Automatic client cleanup and map management
- **Efficient Error Handling**: Reduced error object creation frequency
- **Connection Pooling**: Added read/write deadlines and keepalive

### 🔵 Code Quality Improvements
- **Fixed Naming**: Corrected "manger" → "manager" typo
- **Comprehensive Error Handling**: Added proper error propagation
- **Removed Dead Code**: Cleaned up unused types and methods
- **Race Condition Fixes**: Added proper mutex protection
- **Graceful Shutdown**: Implemented proper server shutdown handling

## 🚀 Features

- **Simple WebSocket Manager**: Easy-to-use WebSocket connection management
- **Event-Based Routing**: Clean event handler registration and routing
- **Client Lifecycle Management**: Automatic connection handling and cleanup
- **Security Features**: CORS protection, connection limits, input validation
- **Error Handling**: Comprehensive error handling with custom handlers
- **Health Monitoring**: Built-in ping/pong keepalive mechanism
- **Graceful Shutdown**: Clean server termination and resource cleanup
- **Customizable**: Pluggable error handlers, configurable limits, custom origins

## 📦 Installation

```bash
go mod tidy
go build .
```

## 🏃 Running

```bash
./wswapper
```

Server starts on `http://localhost:8080/ws`

## 📡 API

### WebSocket Events

#### Ping
```json
{
  "event": "ping",
  "data": {}
}
```

#### Echo
```json
{
  "event": "echo",
  "data": {
    "message": "Hello World"
  }
}
```

#### Broadcast
```json
{
  "event": "broadcast",
  "data": {
    "message": "Hello everyone!",
    "room": "general"
  }
}
```

#### Client Info
```json
{
  "event": "client_info",
  "data": {}
}
```

## 🔒 Security Features

- **Origin Validation**: Only allows localhost connections by default
- **Connection Limits**: Prevents DoS attacks
- **Timeout Protection**: Read/write deadlines prevent hanging connections
- **Resource Cleanup**: Automatic cleanup prevents memory leaks

## ⚙️ Configuration

```go
// Create manager with default settings
manager := DefaultManager()

// Set custom allowed origins
manager.SetAllowedOrigins([]string{
    "https://yourdomain.com",
    "https://www.yourdomain.com",
})

// Set custom error handler
manager.SetErrorHandler(NewDefaultErrorHandler(manager.logger))

// Create event router
router := NewEventRouter()
router.On("ping", handlePing)
router.On("echo", handleEcho)
manager.SetRouter(router)

// Custom client limits
manager.maxClients = 500
```

## 🧪 Testing

```bash
# Test WebSocket connection
wscat -c ws://localhost:8080/ws

# Send ping
{"event":"ping","data":{}}

# Subscribe to topic
{"event":"subscribe","data":{"topic":"test"}}

# Publish message
{"event":"publish","data":{"topic":"test","data":{"msg":"hello"}}}
```

## 📊 Performance Improvements

- **Before**: Potential memory leaks, goroutine leaks, security vulnerabilities
- **After**: Secure, efficient, production-ready WebSocket server

## 🛡️ Security Considerations

1. **CORS**: Configure allowed origins for production
2. **Authentication**: Add JWT or API key validation
3. **Rate Limiting**: Implement per-client message limits
4. **TLS**: Use HTTPS/WSS in production
5. **Monitoring**: Add metrics and logging

## 🔄 Migration Guide

If upgrading from the previous version:

1. Update CORS configuration
2. Handle new client creation return type
3. Update error handlers for nil checks
4. Review topic subscription logic

## 📈 Monitoring

The server provides:
- Client connection counts
- Topic subscription metrics
- Error logging
- Connection lifecycle events

## 🤝 Contributing

1. Follow Go best practices
2. Add tests for new features
3. Update documentation
4. Ensure security considerations

## 📄 License

MIT License - see LICENSE file for details.
