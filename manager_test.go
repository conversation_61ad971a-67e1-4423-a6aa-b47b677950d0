package main

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gorilla/websocket"
)

func TestManagerCreation(t *testing.T) {
	manager := DefaultManager()
	if manager == nil {
		t.<PERSON><PERSON>("DefaultManager() returned nil")
	}

	if manager.GetClientCount() != 0 {
		t.<PERSON><PERSON><PERSON>("Expected 0 clients, got %d", manager.GetClientCount())
	}
}

func TestClientLimits(t *testing.T) {
	manager := DefaultManager()
	manager.maxClients = 2

	// Test by directly manipulating the client count
	// Since we can't easily mock websocket.Conn, we'll test the logic differently

	// Verify initial state
	if manager.GetClientCount() != 0 {
		t.<PERSON><PERSON><PERSON>("Expected 0 clients initially, got %d", manager.GetClientCount())
	}

	// Test maxClients limit by checking the logic
	if manager.maxClients != 2 {
		t.<PERSON><PERSON><PERSON>("Expected maxClients to be 2, got %d", manager.maxClients)
	}

	// Test that the manager has the correct configuration
	if manager.clients == nil {
		t.<PERSON>r("Manager clients map should not be nil")
	}
}

// Mock WebSocket connection for testing
type mockWebSocketConn struct {
	closed bool
}

func (m *mockWebSocketConn) Close() error {
	m.closed = true
	return nil
}

func (m *mockWebSocketConn) ReadMessage() (messageType int, p []byte, err error) {
	return websocket.TextMessage, []byte(`{"event":"test","data":{}}`), nil
}

func (m *mockWebSocketConn) WriteMessage(messageType int, data []byte) error {
	return nil
}

func (m *mockWebSocketConn) WriteJSON(v interface{}) error {
	return nil
}

func (m *mockWebSocketConn) SetReadDeadline(t time.Time) error {
	return nil
}

func (m *mockWebSocketConn) SetWriteDeadline(t time.Time) error {
	return nil
}

func (m *mockWebSocketConn) SetPongHandler(h func(appData string) error) {
}

func TestMessageHandling(t *testing.T) {
	manager := DefaultManager()
	router := NewEventRouter()

	// Set up a test handler
	var receivedMessage *Message
	router.On("test", func(c Ctx) error {
		receivedMessage = c.Data
		response := NewMessage("test_response", []byte(`{"status":"ok"}`))
		return c.WriteMessage(response)
	})

	manager.SetRouter(router)

	// Create a mock connection
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		conn, err := manager.upgrader.Upgrade(w, r, nil)
		if err != nil {
			t.Fatalf("Failed to upgrade connection: %v", err)
		}
		defer conn.Close()

		// Simulate message handling
		testMsg := NewMessage("test", []byte(`{"data":"test_data"}`))
		ctx := Ctx{
			client:  nil,
			manager: manager,
			Data:    testMsg,
		}

		err = router.Handle(ctx)
		if err != nil {
			t.Errorf("Router failed to handle message: %v", err)
		}

		if receivedMessage == nil {
			t.Error("Message was not received by handler")
		} else if receivedMessage.Event != "test" {
			t.Errorf("Expected event 'test', got '%s'", receivedMessage.Event)
		}
	}))
	defer server.Close()
}

func TestErrorHandling(t *testing.T) {
	manager := DefaultManager()

	var capturedError error
	var capturedContext map[string]interface{}

	// Set custom error handler
	manager.SetErrorHandler(&testErrorHandler{
		handleFunc: func(err error, context map[string]interface{}) {
			capturedError = err
			capturedContext = context
		},
	})

	// Trigger an error
	testErr := &testError{message: "test error"}
	manager.errHandler.HandleError(testErr, map[string]interface{}{
		"test_key": "test_value",
	})

	if capturedError == nil {
		t.Error("Error was not captured")
	} else if capturedError.Error() != "test error" {
		t.Errorf("Expected 'test error', got '%s'", capturedError.Error())
	}

	if capturedContext == nil {
		t.Error("Context was not captured")
	} else if capturedContext["test_key"] != "test_value" {
		t.Error("Context was not properly captured")
	}
}

// Test helper types
type testError struct {
	message string
}

func (e *testError) Error() string {
	return e.message
}

type testErrorHandler struct {
	handleFunc func(error, map[string]interface{})
}

func (h *testErrorHandler) HandleError(err error, context map[string]interface{}) {
	if h.handleFunc != nil {
		h.handleFunc(err, context)
	}
}

func TestClientLifecycle(t *testing.T) {
	manager := DefaultManager()
	// Allow all origins for testing
	manager.upgrader.CheckOrigin = func(r *http.Request) bool { return true }

	// Create a mock connection
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		conn, err := manager.upgrader.Upgrade(w, r, nil)
		if err != nil {
			t.Fatalf("Failed to upgrade connection: %v", err)
		}
		defer conn.Close()

		client := manager.AddClient(conn, true)
		if client == nil {
			t.Fatal("Failed to add client")
		}

		if !client.IsOnline() {
			t.Error("Client should be online")
		}

		if !client.IsConnected() {
			t.Error("Client should be connected")
		}

		// Test client removal
		err = manager.RemoveClient(client.Id)
		if err != nil {
			t.Errorf("Failed to remove client: %v", err)
		}

		if manager.GetClientCount() != 0 {
			t.Errorf("Expected 0 clients after removal, got %d", manager.GetClientCount())
		}
	}))
	defer server.Close()
}

func TestBroadcastMessage(t *testing.T) {
	manager := DefaultManager()

	// Test broadcast functionality without actual WebSocket connections
	broadcastMsg := NewMessage("broadcast", []byte(`{"message":"hello all"}`))

	// Should not panic even with no clients
	manager.BroadcastMessage(broadcastMsg)

	// Verify initial state
	if manager.GetClientCount() != 0 {
		t.Errorf("Expected 0 clients, got %d", manager.GetClientCount())
	}

	// Test that broadcast message is properly formed
	if broadcastMsg.Event != "broadcast" {
		t.Errorf("Expected event 'broadcast', got '%s'", broadcastMsg.Event)
	}

	expectedData := `{"message":"hello all"}`
	if string(broadcastMsg.Data) != expectedData {
		t.Errorf("Expected data '%s', got '%s'", expectedData, string(broadcastMsg.Data))
	}
}
