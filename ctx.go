package main

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/gorilla/websocket"
)

type Ctx struct {
	manager *Manager
	client  *Client
	Data    *Message
	ctx     context.Context
}

func NewCtx(manager *Manager, client *Client, conn *websocket.Conn, body *Message, ctx context.Context) Ctx {
	return Ctx{
		manager: manager,
		client:  client,
		Data:    body,
		ctx:     ctx,
	}
}

func (c *Ctx) GetClient() *Client {
	return c.client
}

func (c *Ctx) GetMsg() []byte {
	return c.Data.GetData()
}

// WriteMessage sends a message to the client
func (c *Ctx) WriteMessage(msg *Message) error {
	if c.client == nil || c.client.conn == nil {
		return fmt.Errorf("client connection is nil")
	}

	data, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	return c.client.conn.WriteMessage(websocket.TextMessage, data)
}

// WriteJSON sends JSON data to the client
func (c *Ctx) WriteJSON(data interface{}) error {
	if c.client == nil || c.client.conn == nil {
		return fmt.Errorf("client connection is nil")
	}

	return c.client.conn.WriteJSON(data)
}

func (c *Ctx) Bind(i interface{}) error {
	return c.Data.jsonBind(i)
}

// GetClientID returns the client ID
func (c *Ctx) GetClientID() string {
	if c.client == nil {
		return ""
	}
	return c.client.Id
}

// IsClientOnline returns whether the client is online
func (c *Ctx) IsClientOnline() bool {
	if c.client == nil {
		return false
	}
	return c.client.IsOnline()
}

// BroadcastMessage sends a message to all connected clients
func (c *Ctx) BroadcastMessage(msg *Message) {
	if c.manager != nil {
		c.manager.BroadcastMessage(msg)
	}
}

// SendToClient sends a message to the current client
func (c *Ctx) SendToClient(msg *Message) {
	if c.client != nil {
		c.client.Send(msg)
	}
}

type Message struct {
	Event string          `json:"event"`
	Data  json.RawMessage `json:"data"`
}

func NewMessage(event string, data []byte) *Message {
	return &Message{
		Event: event,
		Data:  data,
	}
}

func NewMessageFromBytes(data []byte) (*Message, error) {
	var msg Message
	err := json.Unmarshal(data, &msg)
	if err != nil {
		return nil, err
	}
	return &msg, nil
}

func (m *Message) GetEvent() string {
	return m.Event
}

func (m *Message) GetData() []byte {
	return m.Data
}

func (m *Message) jsonBind(i interface{}) error {
	return json.Unmarshal(m.Data, i)
}
