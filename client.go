package main

import (
	"context"
	"fmt"
	"sync"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

type Client struct {
	Id            string
	conn          *websocket.Conn
	send          chan *Message
	online        bool
	authenticated bool
	errHandler    func(error)
	closeHandler  func(c *Client) error
	ctx           context.Context
	cancel        context.CancelFunc
	mut           sync.RWMutex
	closeOnce     sync.Once
}

func (c *Client) Close() {
	c.mut.Lock()
	defer c.mut.Unlock()

	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	c.online = false

	// Close send channel if not already closed
	c.closeOnce.Do(func() {
		close(c.send)
	})

	// Call close handler if set
	if c.closeHandler != nil {
		c.closeHandler(c)
	}
}

func (c *Client) SetOnline(b bool) {
	c.mut.Lock()
	defer c.mut.Unlock()
	c.online = b
}

func (c *Client) IsOnline() bool {
	c.mut.RLock()
	defer c.mut.RUnlock()
	return c.online
}

// newClient creates a new Client instance with the provided connection
// If id is empty, a new UUID will be generated
func NewClient(conn *websocket.Conn, closeHandler func(c *Client) error) *Client {
	id := uuid.New().String()

	ctx, cancel := context.WithCancel(context.Background())

	return &Client{
		Id:           id,
		conn:         conn,
		send:         make(chan *Message, 256), // buffered channel
		online:       true,
		closeHandler: closeHandler,
		ctx:          ctx,
		cancel:       cancel,
		mut:          sync.RWMutex{},
	}
}

func (c *Client) SetErrHandler(handler func(error)) {
	c.errHandler = handler
}

func (c *Client) SetCloseHandler(handler func(c *Client) error) {
	c.closeHandler = handler
}

func (c *Client) Send(msg *Message) {
	if !c.IsOnline() {
		return
	}

	select {
	case c.send <- msg:
	case <-c.ctx.Done():
		return
	default:
		if c.errHandler != nil {
			c.errHandler(fmt.Errorf("send channel is full for client %s", c.Id))
		}
	}
}

// IsConnected checks if the client connection is still valid
func (c *Client) IsConnected() bool {
	c.mut.RLock()
	defer c.mut.RUnlock()
	return c.conn != nil && c.online
}
