package main

import (
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

type Manager struct {
	clients    map[string]*Client
	mut        sync.RWMutex
	upgrader   websocket.Upgrader
	router     *EventRouter
	errHandler ErrorHandler
	maxClients int
	config     *Config
	logger     Logger
}

func DefaultManager() *Manager {
	logger := NewDefaultLogger()
	return &Manager{
		clients: make(map[string]*Client),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Default to localhost only for security
				origin := r.Header.Get("Origin")
				allowedOrigins := []string{
					"http://localhost:3000",
					"http://localhost:8080",
					"http://127.0.0.1:3000",
					"http://127.0.0.1:8080",
				}
				for _, allowed := range allowedOrigins {
					if origin == allowed {
						return true
					}
				}
				return false
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		maxClients: 1000,
		errHandler: NewDefaultErrorHandler(logger),
		logger:     logger,
		config:     DefaultConfig(),
	}
}

func CreateManager(upgrader websocket.Upgrader, config *Config) *Manager {
	return &Manager{
		clients:    make(map[string]*Client),
		upgrader:   upgrader,
		maxClients: 1000,
		config:     config,
	}
}

// SetErrorHandler sets the error handler for the manager
func (m *Manager) SetErrorHandler(handler ErrorHandler) {
	m.errHandler = handler
}

// SetRouter sets the event router for the manager
func (m *Manager) SetRouter(router *EventRouter) {
	m.router = router
}

// SetAllowedOrigins sets the allowed origins for CORS
func (m *Manager) SetAllowedOrigins(origins []string) {
	m.config.AllowedOrigins = origins
	m.upgrader.CheckOrigin = func(r *http.Request) bool {
		origin := r.Header.Get("Origin")
		for _, allowed := range origins {
			if strings.Contains(origin, allowed) {
				return true
			}
		}
		return false
	}
}

// GetClientCount returns the current number of connected clients
func (m *Manager) GetClientCount() int {
	m.mut.RLock()
	defer m.mut.RUnlock()
	return len(m.clients)
}

func (m *Manager) AddClient(conn *websocket.Conn, online bool) *Client {
	m.mut.Lock()
	defer m.mut.Unlock()

	// Check client limit before creating client
	if len(m.clients) >= m.maxClients {
		conn.Close()
		return nil
	}

	// Create client with proper close handler
	client := NewClient(conn, func(c *Client) error {
		return m.RemoveClient(c.Id)
	})

	// Set error handler
	client.SetErrHandler(func(err error) {
		if m.errHandler != nil {
			m.errHandler.HandleError(err, map[string]interface{}{
				"client_id": client.Id,
			})
		}
	})

	client.online = online
	m.clients[client.Id] = client

	return client
}

// AddExistingClient adds an existing Client instance to the manager
// Returns true if the client was added successfully, false if a client with the same ID already exists
func (m *Manager) AddExistingClient(client *Client) bool {
	if client == nil {
		return false
	}

	m.mut.Lock()
	defer m.mut.Unlock()

	// Check if client with this ID already exists
	if _, exists := m.clients[client.Id]; exists {
		return false
	}

	// Check client limit
	if len(m.clients) >= m.maxClients {
		return false
	}

	// Add the client to the manager
	m.clients[client.Id] = client
	return true
}

// RemoveClient removes a client from the managerWebSocket manager package built with Go.
func (m *Manager) RemoveClient(clientId string) error {
	m.mut.Lock()
	defer m.mut.Unlock()

	if client, exists := m.clients[clientId]; exists {
		client.Close()
		delete(m.clients, clientId)
		log.Printf("Client disconnected: %s", clientId)
	}
	return nil
}

// GetClient returns a client by ID
func (m *Manager) GetClient(clientId string) (*Client, bool) {
	m.mut.RLock()
	defer m.mut.RUnlock()
	client, exists := m.clients[clientId]
	return client, exists
}

// BroadcastMessage sends a message to all connected clients
func (m *Manager) BroadcastMessage(msg *Message) {
	m.mut.RLock()
	defer m.mut.RUnlock()

	for _, client := range m.clients {
		if client.IsOnline() {
			client.Send(msg)
		}
	}
}

func (m *Manager) BrodcastMessageToGroup(msg *Message, group []string) {
	m.mut.RLock()
	defer m.mut.RUnlock()

	for _, id := range group {
		client, exists := m.clients[id]
		if !exists {
			continue
		}
		if client.IsOnline() {
			client.Send(msg)
		} else {
			log.Printf("Client %s is offline, skipping broadcast", id)
		}
	}
}

func (m *Manager) ReadPump(client *Client) {
	defer func() {
		client.Close()
		m.RemoveClient(client.Id)
		if client.cancel != nil {
			client.cancel()
		}
	}()

	// Set read deadline and pong handler for keepalive
	client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	client.conn.SetPongHandler(func(string) error {
		client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		select {
		case <-client.ctx.Done():
			return
		default:
			_, msg, err := client.conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("WebSocket error: %v", err)
				}
				return
			}

			message, err := NewMessageFromBytes(msg)
			if err != nil {
				if m.errHandler != nil {
					m.errHandler.HandleError(err, map[string]interface{}{
						"client_id": client.Id,
						"action":    "parse_message",
						"raw_data":  string(msg),
					})
				}
				continue
			}

			// Create context for handling the incoming message
			ctx := Ctx{
				client:  client,
				manager: m,
				Data:    message,
				ctx:     client.ctx,
			}

			// Handle the message through the router
			if m.router != nil {
				err := m.router.Handle(ctx)
				if err != nil {
					if m.errHandler != nil {
						m.errHandler.HandleError(err, map[string]interface{}{
							"client_id": client.Id,
							"event":     message.Event,
							"action":    "handle_message",
						})
					}
				}
			}
		}
	}
}

func (m *Manager) WritePump(client *Client) {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		m.RemoveClient(client.Id)
		if client.cancel != nil {
			client.cancel()
		}
	}()

	for {
		select {
		case <-client.ctx.Done():
			return

		case msg, ok := <-client.send:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				client.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			// Marshal message to JSON and write to WebSocket
			if err := client.conn.WriteJSON(msg); err != nil {
				if m.errHandler != nil {
					m.errHandler.HandleError(err, map[string]interface{}{
						"client_id": client.Id,
						"event":     msg.Event,
						"action":    "write_message",
					})
				}
				return
			}

		case <-ticker.C:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := client.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

type Config struct {
	ReadTimeout       time.Duration `json:"read_timeout"`
	WriteTimeout      time.Duration `json:"write_timeout"`
	PingInterval      time.Duration `json:"ping_interval"`
	PongTimeout       time.Duration `json:"pong_timeout"`
	ReadBufferSize    int           `json:"read_buffer_size"`
	WriteBufferSize   int           `json:"write_buffer_size"`
	SendChannelSize   int           `json:"send_channel_size"`
	MaxClients        int           `json:"max_clients"`
	MaxMessageSize    int           `json:"max_message_size"`
	AllowedOrigins    []string      `json:"allowed_origins"`
	EnableCompression bool          `json:"enable_compression"`
	EnablePing        bool          `json:"enable_ping"`
	LogConnection     bool          `json:"log_connection"`
	LogError          bool          `json:"log_error"`
}

func DefaultConfig() *Config {
	return &Config{
		ReadTimeout:  60 * time.Second,
		WriteTimeout: 10 * time.Second,
		PingInterval: 54 * time.Second,
		PongTimeout:  10 * time.Second,

		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		SendChannelSize: 100,

		MaxClients:     1000,
		MaxMessageSize: 1024 * 1024, // 1 MB
		AllowedOrigins: []string{"http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000", "http://127.0.0.1:8080"},

		EnableCompression: false,
		EnablePing:        true,
		LogConnection:     true,
		LogError:          true,
	}
}
