package main

import "go.uber.org/zap"

// Logger interface for pluggable logging
type Logger interface {
	Debug(msg string, fields ...zap.Field)
	Info(msg string, fields ...zap.Field)
	Warn(msg string, fields ...zap.Field)
	Error(msg string, fields ...zap.Field)
	Fatal(msg string, fields ...zap.Field)
	With(fields ...zap.Field) Logger
}

// DefaultLogger wraps zap logger
type DefaultLogger struct {
	logger *zap.Logger
}

func NewDefaultLogger() Logger {
	logger, _ := zap.NewDevelopment()
	return &DefaultLogger{logger: logger}
}

func (l *DefaultLogger) Debug(msg string, fields ...zap.Field) { l.logger.Debug(msg, fields...) }
func (l *DefaultLogger) Info(msg string, fields ...zap.Field)  { l.logger.Info(msg, fields...) }
func (l *DefaultLogger) Warn(msg string, fields ...zap.Field)  { l.logger.Warn(msg, fields...) }
func (l *DefaultLogger) Error(msg string, fields ...zap.Field) { l.logger.Error(msg, fields...) }
func (l *DefaultLogger) Fatal(msg string, fields ...zap.Field) { l.logger.Fatal(msg, fields...) }
func (l *DefaultLogger) With(fields ...zap.Field) Logger {
	return &DefaultLogger{logger: l.logger.With(fields...)}
}

// ErrorHandler interface for pluggable error handling
type ErrorHandler interface {
	HandleError(err error, context map[string]interface{})
}

// DefaultErrorHandler provides basic error handling
type DefaultErrorHandler struct {
	logger Logger
}

func NewDefaultErrorHandler(logger Logger) ErrorHandler {
	return &DefaultErrorHandler{logger: logger}
}

func (h *DefaultErrorHandler) HandleError(err error, context map[string]interface{}) {
	fields := make([]zap.Field, 0, len(context))
	for k, v := range context {
		fields = append(fields, zap.Any(k, v))
	}
	h.logger.Error("Error occurred", append(fields, zap.Error(err))...)
}
