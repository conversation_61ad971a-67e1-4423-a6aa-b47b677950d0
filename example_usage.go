package main

import (
	"encoding/json"
	"log"
	"net/http"
	"time"
)

// Example demonstrating how to use the WebSocket manager
func ExampleUsage() {
	// Create a manager with default settings
	manager := DefaultManager()

	// Customize error handling
	manager.SetErrorHandler(NewDefaultErrorHandler(manager.logger))

	// Set up custom allowed origins for production
	manager.SetAllowedOrigins([]string{
		"https://yourdomain.com",
		"https://www.yourdomain.com",
		"http://localhost:3000", // for development
	})

	// Create event router and add handlers
	router := NewEventRouter()

	// Basic ping handler
	router.On("ping", func(c Ctx) error {
		response := NewMessage("pong", []byte(`{"timestamp":"`+time.Now().Format(time.RFC3339)+`"}`))
		return c.WriteMessage(response)
	})

	// Echo handler
	router.On("echo", func(c Ctx) error {
		var request struct {
			Message string `json:"message"`
		}

		if err := c.Bind(&request); err != nil {
			errorResponse := NewMessage("error", []byte(`{"error":"Invalid message format"}`))
			return c.WriteMessage(errorResponse)
		}

		response := NewMessage("echo_response", []byte(`{"echo":"`+request.Message+`"}`))
		return c.WriteMessage(response)
	})

	// Broadcast handler
	router.On("broadcast", func(c Ctx) error {
		var request struct {
			Message string `json:"message"`
			Room    string `json:"room,omitempty"`
		}

		if err := c.Bind(&request); err != nil {
			errorResponse := NewMessage("error", []byte(`{"error":"Invalid broadcast format"}`))
			return c.WriteMessage(errorResponse)
		}

		// Create broadcast message
		broadcastData, _ := json.Marshal(map[string]interface{}{
			"message":   request.Message,
			"from":      c.GetClientID(),
			"room":      request.Room,
			"timestamp": time.Now().Format(time.RFC3339),
		})

		broadcastMsg := NewMessage("broadcast_message", broadcastData)
		c.BroadcastMessage(broadcastMsg)

		// Send confirmation to sender
		confirmation := NewMessage("broadcast_sent", []byte(`{"status":"Message broadcasted"}`))
		return c.WriteMessage(confirmation)
	})

	// Client info handler
	router.On("client_info", func(c Ctx) error {
		info, _ := json.Marshal(map[string]interface{}{
			"client_id":     c.GetClientID(),
			"online":        c.IsClientOnline(),
			"total_clients": manager.GetClientCount(),
		})

		response := NewMessage("client_info_response", info)
		return c.WriteMessage(response)
	})

	// Set the router
	manager.SetRouter(router)

	// HTTP handler for WebSocket upgrade
	http.HandleFunc("/ws", func(w http.ResponseWriter, r *http.Request) {
		handleWebSocketConnection(manager, w, r)
	})

	// Serve static files for testing
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/" {
			serveTestPage(w, r)
		} else {
			http.NotFound(w, r)
		}
	})

	log.Println("WebSocket server starting on :8080")
	log.Println("Test page available at http://localhost:8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}

func handleWebSocketConnection(manager *Manager, w http.ResponseWriter, r *http.Request) {
	// Log connection attempt
	log.Printf("WebSocket connection attempt from %s", r.RemoteAddr)

	conn, err := manager.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}

	client := manager.AddClient(conn, true)
	if client == nil {
		log.Printf("Failed to add client - server at capacity")
		conn.Close()
		return
	}

	log.Printf("Client connected: %s (Total clients: %d)", client.Id, manager.GetClientCount())

	// Send welcome message
	welcomeMsg := NewMessage("welcome", []byte(`{
		"message": "Welcome to WebSocket server!",
		"client_id": "`+client.Id+`",
		"server_time": "`+time.Now().Format(time.RFC3339)+`"
	}`))
	client.Send(welcomeMsg)

	// Start client goroutines
	go manager.ReadPump(client)
	go manager.WritePump(client)
}

func serveTestPage(w http.ResponseWriter, _ *http.Request) {
	html := `<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #messages { border: 1px solid #ccc; height: 300px; overflow-y: scroll; padding: 10px; margin: 10px 0; }
        .message { margin: 5px 0; padding: 5px; background: #f0f0f0; border-radius: 3px; }
        .sent { background: #e3f2fd; }
        .received { background: #f3e5f5; }
        .error { background: #ffebee; color: #c62828; }
        input, button { margin: 5px; padding: 8px; }
        #messageInput { width: 300px; }
    </style>
</head>
<body>
    <h1>WebSocket Test Client</h1>
    <div id="status">Disconnected</div>
    <div id="messages"></div>
    <div>
        <input type="text" id="messageInput" placeholder="Enter message..." />
        <button onclick="sendPing()">Ping</button>
        <button onclick="sendEcho()">Echo</button>
        <button onclick="sendBroadcast()">Broadcast</button>
        <button onclick="getClientInfo()">Client Info</button>
    </div>

    <script>
        let ws;
        let clientId = null;

        function connect() {
            ws = new WebSocket('ws://localhost:8080/ws');

            ws.onopen = function() {
                document.getElementById('status').textContent = 'Connected';
                addMessage('Connected to server', 'system');
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                addMessage('Received: ' + JSON.stringify(data, null, 2), 'received');

                if (data.event === 'welcome') {
                    const welcomeData = JSON.parse(data.data);
                    clientId = welcomeData.client_id;
                }
            };

            ws.onclose = function() {
                document.getElementById('status').textContent = 'Disconnected';
                addMessage('Disconnected from server', 'system');
            };

            ws.onerror = function(error) {
                addMessage('Error: ' + error, 'error');
            };
        }

        function addMessage(text, type) {
            const messages = document.getElementById('messages');
            const div = document.createElement('div');
            div.className = 'message ' + type;
            div.textContent = new Date().toLocaleTimeString() + ' - ' + text;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }

        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
                addMessage('Sent: ' + JSON.stringify(message), 'sent');
            } else {
                addMessage('Not connected', 'error');
            }
        }

        function sendPing() {
            sendMessage({event: 'ping', data: {}});
        }

        function sendEcho() {
            const input = document.getElementById('messageInput');
            sendMessage({event: 'echo', data: {message: input.value || 'Hello World'}});
        }

        function sendBroadcast() {
            const input = document.getElementById('messageInput');
            sendMessage({event: 'broadcast', data: {message: input.value || 'Broadcast message'}});
        }

        function getClientInfo() {
            sendMessage({event: 'client_info', data: {}});
        }

        // Auto-connect on page load
        connect();
    </script>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html")
	w.Write([]byte(html))
}

// Uncomment to run the example
// func main() {
//     ExampleUsage()
// }
